<?php
// 创建SQLite数据库和基本表结构
echo "开始创建SQLite数据库...\n";

$dbPath = __DIR__ . '/data/hongmao.db';

// 确保data目录存在
if (!is_dir(__DIR__ . '/data')) {
    mkdir(__DIR__ . '/data', 0755, true);
    echo "创建data目录\n";
}

try {
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "SQLite数据库连接成功\n";
    
    // 创建产品表 (fh_product)
    $productSql = "
    CREATE TABLE IF NOT EXISTS fh_product (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code VARCHAR(20) NOT NULL,
        name VARCHAR(100) NOT NULL,
        price DECIMAL(10,3) DEFAULT 0,
        price_high DECIMAL(10,3) DEFAULT 0,
        price_low DECIMAL(10,3) DEFAULT 0,
        open_price DECIMAL(10,3) DEFAULT 0,
        price_zf DECIMAL(5,2) DEFAULT 0,
        vol BIGINT DEFAULT 0,
        price_update VARCHAR(20),
        time VARCHAR(20),
        change_amount DECIMAL(10,3) DEFAULT 0,
        change_percent DECIMAL(5,2) DEFAULT 0,
        volume BIGINT DEFAULT 0,
        turnover DECIMAL(15,2) DEFAULT 0,
        high DECIMAL(10,3) DEFAULT 0,
        low DECIMAL(10,3) DEFAULT 0,
        open DECIMAL(10,3) DEFAULT 0,
        close DECIMAL(10,3) DEFAULT 0,
        cid INTEGER DEFAULT 2,
        status INTEGER DEFAULT 1,
        is_open INTEGER DEFAULT 1,
        updatetime INTEGER,
        createtime INTEGER
    )";

    $pdo->exec($productSql);
    echo "创建产品表成功\n";

    // 创建股票表
    $sql = "
    CREATE TABLE IF NOT EXISTS fh_stock (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code VARCHAR(20) NOT NULL,
        name VARCHAR(100) NOT NULL,
        price DECIMAL(10,3) DEFAULT 0,
        change_amount DECIMAL(10,3) DEFAULT 0,
        change_percent DECIMAL(5,2) DEFAULT 0,
        volume BIGINT DEFAULT 0,
        turnover DECIMAL(15,2) DEFAULT 0,
        high DECIMAL(10,3) DEFAULT 0,
        low DECIMAL(10,3) DEFAULT 0,
        open DECIMAL(10,3) DEFAULT 0,
        close DECIMAL(10,3) DEFAULT 0,
        updatetime INTEGER,
        createtime INTEGER,
        status VARCHAR(10) DEFAULT 'normal'
    )";

    $pdo->exec($sql);
    echo "创建股票表成功\n";

    // 插入AG0的产品数据
    $insertProductSql = "
    INSERT OR REPLACE INTO fh_product
    (code, name, price, cid, status, is_open, updatetime, createtime)
    VALUES
    ('AG0', '白银主力', 9216.814, 2, 1, 1, " . time() . ", " . time() . ")";

    $pdo->exec($insertProductSql);
    echo "插入AG0产品数据成功\n";

    // 插入AG0的股票数据
    $insertSql = "
    INSERT OR REPLACE INTO fh_stock
    (code, name, price, updatetime, createtime, status)
    VALUES
    ('AG0', '白银主力', 9216.814, " . time() . ", " . time() . ", 'normal')";

    $pdo->exec($insertSql);
    echo "插入AG0股票数据成功\n";
    
    // 验证产品数据
    $stmt = $pdo->query("SELECT * FROM fh_product WHERE code = 'AG0'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result) {
        echo "验证成功，AG0产品数据：\n";
        print_r($result);
    }

    // 验证股票数据
    $stmt = $pdo->query("SELECT * FROM fh_stock WHERE code = 'AG0'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result) {
        echo "验证成功，AG0股票数据：\n";
        print_r($result);
    }
    
    echo "SQLite数据库创建完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
