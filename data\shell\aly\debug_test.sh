#!/bin/bash
# 调试脚本 - 检查执行环境和路径

echo "=== Debug Test Script ===" > /tmp/aly_debug.log
echo "Time: $(date)" >> /tmp/aly_debug.log
echo "Current directory: $(pwd)" >> /tmp/aly_debug.log
echo "Script location: $0" >> /tmp/aly_debug.log
echo "" >> /tmp/aly_debug.log

# 检查PHP路径
echo "=== PHP Path Check ===" >> /tmp/aly_debug.log
which php >> /tmp/aly_debug.log 2>&1
echo "PHP version:" >> /tmp/aly_debug.log
php -v >> /tmp/aly_debug.log 2>&1
echo "" >> /tmp/aly_debug.log

# 检查PHP70路径
echo "=== PHP70 Path Check ===" >> /tmp/aly_debug.log
ls -la /www/server/php/ >> /tmp/aly_debug.log 2>&1
echo "" >> /tmp/aly_debug.log

# 检查stock.php文件
echo "=== Stock.php Check ===" >> /tmp/aly_debug.log
ls -la /www/wwwroot/*********/stock.php >> /tmp/aly_debug.log 2>&1
echo "" >> /tmp/aly_debug.log

# 测试PHP执行
echo "=== PHP Execution Test ===" >> /tmp/aly_debug.log
cd /www/wwwroot/*********/
php stock.php >> /tmp/aly_debug.log 2>&1
echo "Exit code: $?" >> /tmp/aly_debug.log
echo "" >> /tmp/aly_debug.log

# 测试Aly控制器
echo "=== Aly Controller Test ===" >> /tmp/aly_debug.log
php stock.php Aly/runStock >> /tmp/aly_debug.log 2>&1
echo "Exit code: $?" >> /tmp/aly_debug.log

echo "=== Debug Complete ===" >> /tmp/aly_debug.log
