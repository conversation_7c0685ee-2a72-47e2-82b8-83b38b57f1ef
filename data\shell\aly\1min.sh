#!/bin/bash
# 计划任务中每分钟执行一次 获取1分钟k线
# 修改为直接调用，不创建子进程

# 设置工作目录
cd /www/wwwroot/127.0.0.3/

# 创建日志文件
LOG_FILE="/tmp/kline_1min_$(date +%Y%m%d).log"

# 检查PHP路径并执行
if [ -f "/www/server/php/70/bin/php" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Executing 1min kline" >> $LOG_FILE
    /www/server/php/70/bin/php stock.php Aly/runLastData/type/1min >> $LOG_FILE 2>&1
elif [ -f "/www/server/php/74/bin/php" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Executing 1min kline" >> $LOG_FILE
    /www/server/php/74/bin/php stock.php Aly/runLastData/type/1min >> $LOG_FILE 2>&1
elif [ -f "/www/server/php/80/bin/php" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Executing 1min kline" >> $LOG_FILE
    /www/server/php/80/bin/php stock.php Aly/runLastData/type/1min >> $LOG_FILE 2>&1
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Executing 1min kline" >> $LOG_FILE
    php stock.php Aly/runLastData/type/1min >> $LOG_FILE 2>&1
fi
