<?php
define('APP_PATH', __DIR__ . '/application/');
require __DIR__ . '/thinkphp/start.php';

echo "=== 测试API调用（不涉及数据库）===\n";

try {
    // 直接实例化Aly控制器
    $aly = new \app\stock\controller\Aly();
    
    echo "Aly控制器实例化成功\n";
    
    // 测试API请求方法
    $url = "https://finance.market.alicloudapi.com/neipan/real?symbol=BTC";
    
    // 模拟API请求
    $headers = [
        "Authorization: APPCODE ********************************",
        "Content-Type: application/json; charset=utf-8"
    ];

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL            => $url,
        CURLOPT_HTTPHEADER     => $headers,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_TIMEOUT        => 10
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        echo "CURL错误: " . curl_error($ch) . "\n";
    } else {
        echo "HTTP状态码: $httpCode\n";
        echo "API响应: " . substr($response, 0, 200) . "...\n";
        
        $result = json_decode($response, true);
        if ($result) {
            echo "JSON解析成功\n";
            if (isset($result['data'])) {
                echo "API返回数据结构正常\n";
            } else {
                echo "API返回数据结构: " . print_r(array_keys($result), true) . "\n";
            }
        } else {
            echo "JSON解析失败\n";
        }
    }
    curl_close($ch);
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

echo "=== 测试完成 ===\n";
