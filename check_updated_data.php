<?php
// 检查更新后的数据
echo "检查数据库中的最新数据...\n";

$dbPath = __DIR__ . '/data/hongmao.db';

try {
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查询产品表数据
    $stmt = $pdo->query("SELECT * FROM fh_product WHERE code = 'AG0'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "=== AG0产品数据 ===\n";
        echo "代码: " . $result['code'] . "\n";
        echo "名称: " . $result['name'] . "\n";
        echo "价格: " . $result['price'] . "\n";
        echo "最高价: " . $result['price_high'] . "\n";
        echo "最低价: " . $result['price_low'] . "\n";
        echo "开盘价: " . $result['open_price'] . "\n";
        echo "涨跌幅: " . $result['price_zf'] . "%\n";
        echo "成交量: " . $result['vol'] . "\n";
        echo "更新时间: " . $result['price_update'] . "\n";
        echo "时间戳: " . $result['time'] . "\n";
        echo "\n";
    }
    
    // 查询股票表数据
    $stmt = $pdo->query("SELECT * FROM fh_stock WHERE code = 'AG0'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        echo "=== AG0股票数据 ===\n";
        echo "代码: " . $result['code'] . "\n";
        echo "名称: " . $result['name'] . "\n";
        echo "价格: " . $result['price'] . "\n";
        echo "最高价: " . $result['high'] . "\n";
        echo "最低价: " . $result['low'] . "\n";
        echo "开盘价: " . $result['open'] . "\n";
        echo "收盘价: " . $result['close'] . "\n";
        echo "成交量: " . $result['volume'] . "\n";
        echo "更新时间: " . date('Y-m-d H:i:s', $result['updatetime']) . "\n";
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
