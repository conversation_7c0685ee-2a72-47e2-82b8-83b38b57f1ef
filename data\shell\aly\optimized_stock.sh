#!/bin/bash
# 优化的股票数据采集脚本
# 减少API调用频率，避免服务器负载过高

cd /www/wwwroot/127.0.0.3/

# 配置参数
PIDFILE="/tmp/optimized_stock.pid"
LOGFILE="/www/wwwroot/127.0.0.3/runtime/log/optimized_stock.log"
MAX_CONCURRENT=3  # 最大并发进程数

# 检查是否已有进程在运行
if [ -f "$PIDFILE" ]; then
    PID=$(cat "$PIDFILE")
    if ps -p $PID > /dev/null 2>&1; then
        echo "$(date): Process already running with PID $PID" >> $LOGFILE
        exit 1
    fi
fi

# 写入当前进程PID
echo $$ > "$PIDFILE"

# 清理函数
cleanup() {
    echo "$(date): Cleaning up and exiting..." >> $LOGFILE
    rm -f "$PIDFILE"
    exit 0
}

# 设置信号处理
trap cleanup SIGTERM SIGINT

echo "$(date): Starting optimized stock data collection..." >> $LOGFILE

while true
do
    # 检查当前时间，只在交易时间内运行
    HOUR=$(date +%H)
    MINUTE=$(date +%M)
    DAY_OF_WEEK=$(date +%u)  # 1-7 (Monday-Sunday)
    
    # 只在工作日的交易时间内运行 (9:00-16:00)
    if [ $DAY_OF_WEEK -le 5 ] && [ $HOUR -ge 9 ] && [ $HOUR -le 16 ]; then
        
        # 1分钟K线数据 - 每2分钟执行一次
        if [ $((MINUTE % 2)) -eq 0 ]; then
            echo "$(date): Running 1min data collection..." >> $LOGFILE
            /www/server/php/70/bin/php stock.php Aly/runLastData/type/1min >> $LOGFILE 2>&1 &
        fi
        
        # 5分钟K线数据 - 每5分钟执行一次
        if [ $((MINUTE % 5)) -eq 0 ]; then
            echo "$(date): Running 5min data collection..." >> $LOGFILE
            /www/server/php/70/bin/php stock.php Aly/runLastData/type/5min >> $LOGFILE 2>&1 &
        fi
        
        # 30分钟K线数据 - 每30分钟执行一次
        if [ $((MINUTE % 30)) -eq 0 ]; then
            echo "$(date): Running 30min data collection..." >> $LOGFILE
            /www/server/php/70/bin/php stock.php Aly/runLastData/type/30min >> $LOGFILE 2>&1 &
        fi
        
        # 1小时K线数据 - 每小时执行一次
        if [ $MINUTE -eq 0 ]; then
            echo "$(date): Running 1hour data collection..." >> $LOGFILE
            /www/server/php/70/bin/php stock.php Aly/runLastData/type/1hour >> $LOGFILE 2>&1 &
        fi
        
        # 控制并发进程数量
        while [ $(jobs -r | wc -l) -ge $MAX_CONCURRENT ]; do
            sleep 5
        done
        
    else
        echo "$(date): Outside trading hours, sleeping..." >> $LOGFILE
    fi
    
    # 每分钟检查一次
    sleep 60
done
