<?php
define('APP_PATH', __DIR__ . '/application/');
require __DIR__ . '/thinkphp/start.php';

echo "=== 测试runStock方法 ===\n";

try {
    // 直接实例化Aly控制器
    $aly = new \app\stock\controller\Aly();
    
    echo "Aly控制器实例化成功\n";
    
    // 测试runStock方法
    echo "测试runStock方法...\n";
    $aly->runStock();
    echo "runStock方法执行完成\n";
    
} catch (ParseError $e) {
    echo "解析错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "致命错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n";
    echo $e->getTraceAsString() . "\n";
}

echo "=== 测试完成 ===\n";
