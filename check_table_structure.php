<?php
// 检查数据库表结构
echo "=== 检查数据库表结构 ===\n";

$dbPath = __DIR__ . '/data/hongmao.db';

try {
    $pdo = new PDO('sqlite:' . $dbPath);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 查询表结构
    $stmt = $pdo->query("PRAGMA table_info(fh_product)");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "fh_product 表字段:\n";
    foreach ($columns as $column) {
        echo "- " . $column['name'] . " (" . $column['type'] . ")\n";
    }
    
    echo "\n当前数据:\n";
    $stmt = $pdo->query("SELECT * FROM fh_product WHERE code = 'AG0'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        foreach ($result as $key => $value) {
            echo "$key: $value\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
