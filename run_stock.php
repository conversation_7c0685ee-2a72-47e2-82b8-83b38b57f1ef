<?php
// 直接执行股票价格更新脚本
echo "开始执行股票价格更新...\n";

define('APP_PATH', __DIR__ . '/application/');
define('BIND_MODULE','stock');
require __DIR__ . '/thinkphp/start.php';

try {
    echo "正在实例化 Aly 控制器...\n";
    $aly = new \app\stock\controller\Aly();
    
    echo "开始执行 runStock 方法...\n";
    $aly->runStock();
    
    echo "股票价格更新完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
