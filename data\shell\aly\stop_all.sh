#!/bin/bash
# 停止所有股票数据采集进程

echo "Stopping all stock data collection processes..."

# 停止优化脚本
if [ -f "/tmp/optimized_stock.pid" ]; then
    PID=$(cat "/tmp/optimized_stock.pid")
    if ps -p $PID > /dev/null 2>&1; then
        echo "Stopping optimized stock process (PID: $PID)..."
        kill $PID
        rm -f "/tmp/optimized_stock.pid"
    fi
fi

# 停止kline脚本
if [ -f "/tmp/kline_1min.pid" ]; then
    PID=$(cat "/tmp/kline_1min.pid")
    if ps -p $PID > /dev/null 2>&1; then
        echo "Stopping kline process (PID: $PID)..."
        kill $PID
        rm -f "/tmp/kline_1min.pid"
    fi
fi

# 杀死所有相关的PHP进程
echo "Killing remaining PHP processes..."
pkill -f "stock.php"
pkill -f "Aly/runLastData"
pkill -f "Aly/getLastData"
pkill -f "Aly/runStock"
pkill -f "Aly/getStock"

echo "All processes stopped."
