#!/bin/bash
# 获取最新价格数据脚本 - 宝塔计划任务版本

# 设置工作目录
cd /www/wwwroot/*********/

# 创建日志文件
LOG_FILE="/tmp/stock_$(date +%Y%m%d).log"

# 记录脚本开始
echo "$(date '+%Y-%m-%d %H:%M:%S') - Stock script started" >> $LOG_FILE

# 检查PHP路径
PHP_PATH=""
if [ -f "/www/server/php/70/bin/php" ]; then
    PHP_PATH="/www/server/php/70/bin/php"
elif [ -f "/www/server/php/74/bin/php" ]; then
    PHP_PATH="/www/server/php/74/bin/php"
elif [ -f "/www/server/php/80/bin/php" ]; then
    PHP_PATH="/www/server/php/80/bin/php"
elif [ -f "/usr/bin/php" ]; then
    PHP_PATH="/usr/bin/php"
else
    PHP_PATH="php"
fi

echo "$(date '+%Y-%m-%d %H:%M:%S') - Using PHP: $PHP_PATH" >> $LOG_FILE

# 执行一次获取股票数据
echo "$(date '+%Y-%m-%d %H:%M:%S') - Executing runStock" >> $LOG_FILE
$PHP_PATH stock.php Aly/runStock >> $LOG_FILE 2>&1
exit_code=$?

if [ $exit_code -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Stock script completed successfully" >> $LOG_FILE
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Error: Exit code $exit_code" >> $LOG_FILE
fi