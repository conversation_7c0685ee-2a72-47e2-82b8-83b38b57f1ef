<?php
// 直接测试API调用
echo "=== 直接测试API调用 ===\n";

$url = "http://137.220.153.53/api/index/goods_stock?symbol=AG0&resolution=1hour";

echo "请求URL: " . $url . "\n";

// 使用CURL获取数据
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: " . $httpCode . "\n";

if ($error) {
    echo "CURL错误: " . $error . "\n";
} else {
    echo "响应长度: " . strlen($response) . " 字节\n";
    
    if ($response) {
        // 尝试解析JSON
        $data = json_decode($response, true);
        
        if ($data) {
            echo "JSON解析成功\n";
            echo "数据结构:\n";
            
            if (isset($data['data']) && is_array($data['data'])) {
                echo "找到 " . count($data['data']) . " 条数据记录\n";
                
                // 显示最后几条数据
                $lastItems = array_slice($data['data'], -3);
                foreach ($lastItems as $index => $item) {
                    echo "\n--- 数据 " . ($index + 1) . " ---\n";
                    if (isset($item['time'])) echo "时间: " . date('Y-m-d H:i:s', $item['time']) . "\n";
                    if (isset($item['open'])) echo "开盘: " . $item['open'] . "\n";
                    if (isset($item['high'])) echo "最高: " . $item['high'] . "\n";
                    if (isset($item['low'])) echo "最低: " . $item['low'] . "\n";
                    if (isset($item['close'])) echo "收盘: " . $item['close'] . "\n";
                    if (isset($item['volume'])) echo "成交量: " . $item['volume'] . "\n";
                }
                
                // 获取最新价格
                if (!empty($data['data'])) {
                    $latest = end($data['data']);
                    echo "\n=== 最新价格信息 ===\n";
                    echo "最新价格: " . ($latest['close'] ?? 'N/A') . "\n";
                    echo "时间: " . (isset($latest['time']) ? date('Y-m-d H:i:s', $latest['time']) : 'N/A') . "\n";
                }
            } else {
                echo "数据格式不符合预期\n";
                echo "原始响应前500字符:\n";
                echo substr($response, 0, 500) . "\n";
            }
        } else {
            echo "JSON解析失败\n";
            echo "原始响应前500字符:\n";
            echo substr($response, 0, 500) . "\n";
        }
    } else {
        echo "响应为空\n";
    }
}

echo "\n=== 测试完成 ===\n";
