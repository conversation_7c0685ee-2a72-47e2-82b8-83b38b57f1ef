<?php
define('APP_PATH', __DIR__ . '/application/');
require __DIR__ . '/thinkphp/start.php';

use think\Db;

echo "=== 测试数据库查询 ===\n";

try {
    // 测试数据库查询
    echo "查询产品数据...\n";
    $stocks = Db::name('product')->where(['status' => 1, 'is_open' => 1, 'cid' => 2])->select();
    
    echo "找到 " . count($stocks) . " 个产品\n";
    
    if (count($stocks) > 0) {
        echo "前3个产品:\n";
        foreach (array_slice($stocks, 0, 3) as $stock) {
            echo "代码: {$stock['code']}, 价格: {$stock['price']}, 更新时间: {$stock['price_update']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

echo "=== 测试完成 ===\n";
