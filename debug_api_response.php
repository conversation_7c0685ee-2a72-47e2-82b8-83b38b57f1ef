<?php
// 调试API响应
echo "=== 调试API响应 ===\n";

define('APP_PATH', __DIR__ . '/application/');
define('BIND_MODULE','stock');
require __DIR__ . '/thinkphp/start.php';

try {
    $aly = new \app\stock\controller\Aly();
    
    // 直接调用API
    $url = "http://137.220.153.53/api/index/goods_stock?symbol=AG0&resolution=1hour";
    echo "请求URL: $url\n";
    
    // 使用反射来访问私有方法
    $reflection = new ReflectionClass($aly);
    $method = $reflection->getMethod('aliyunRequest');
    $method->setAccessible(true);
    
    $result = $method->invoke($aly, $url);
    
    echo "\nAPI响应结构:\n";
    if (isset($result['data']) && is_array($result['data'])) {
        echo "数据条数: " . count($result['data']) . "\n";
        
        // 显示最后3条数据
        $lastItems = array_slice($result['data'], -3);
        foreach ($lastItems as $index => $item) {
            echo "\n--- 数据 " . ($index + 1) . " ---\n";
            foreach ($item as $key => $value) {
                if ($key == 'time') {
                    echo "$key: $value (" . date('Y-m-d H:i:s', $value) . ")\n";
                } else {
                    echo "$key: $value\n";
                }
            }
        }
        
        // 获取最新数据
        $latestData = end($result['data']);
        echo "\n=== 最新数据处理 ===\n";
        echo "原始close价格: " . $latestData['close'] . "\n";
        echo "原始high价格: " . $latestData['high'] . "\n";
        echo "原始low价格: " . $latestData['low'] . "\n";
        echo "原始open价格: " . $latestData['open'] . "\n";
        echo "原始volume: " . $latestData['volume'] . "\n";
        echo "原始time: " . $latestData['time'] . " (" . date('Y-m-d H:i:s', $latestData['time']) . ")\n";
        
        // 模拟getFormat处理
        $priceData = [
            'price' => $latestData['close'],
            'high' => $latestData['high'],
            'low' => $latestData['low'],
            'open' => $latestData['open'],
            'volume' => $latestData['volume'],
            'update_time' => $latestData['time']
        ];
        
        echo "\n=== 处理后的数据 ===\n";
        foreach ($priceData as $key => $value) {
            echo "$key: $value\n";
        }
        
    } else {
        echo "API响应格式异常\n";
        var_dump($result);
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
