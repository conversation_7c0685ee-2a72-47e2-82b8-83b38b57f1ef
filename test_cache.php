<?php
define('APP_PATH', __DIR__ . '/application/');
require __DIR__ . '/thinkphp/start.php';

use think\Cache;
use think\Config;

echo "=== 缓存配置测试 ===\n";

// 显示当前缓存配置
echo "当前缓存配置:\n";
$cacheConfig = Config::get('cache');
print_r($cacheConfig);

echo "\n=== 测试缓存操作 ===\n";

try {
    // 测试缓存写入
    echo "测试缓存写入...\n";
    $result = Cache::set('test_key', 'test_value', 60);
    echo "写入结果: " . ($result ? '成功' : '失败') . "\n";
    
    // 测试缓存读取
    echo "测试缓存读取...\n";
    $value = Cache::get('test_key');
    echo "读取结果: " . $value . "\n";
    
    // 测试缓存删除
    echo "测试缓存删除...\n";
    $result = Cache::rm('test_key');
    echo "删除结果: " . ($result ? '成功' : '失败') . "\n";
    
    echo "\n缓存测试完成，没有错误！\n";
    
} catch (Exception $e) {
    echo "缓存测试失败: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
}

echo "\n=== 测试数据库连接 ===\n";

use think\Db;

try {
    
    // 测试数据库连接
    $count = Db::name('product')->where(['status' => 1, 'is_open' => 1, 'cid' => 2])->count();
    echo "找到 {$count} 个产品\n";
    
    if ($count > 0) {
        $products = Db::name('product')->where(['status' => 1, 'is_open' => 1, 'cid' => 2])->limit(3)->field('code,price,price_update')->select();
        echo "前3个产品:\n";
        foreach ($products as $product) {
            echo "代码: {$product['code']}, 价格: {$product['price']}, 更新时间: {$product['price_update']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "数据库测试失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
