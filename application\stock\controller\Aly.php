<?php
namespace app\stock\controller;

use think\Controller;
use think\Request;
use think\Db;
use think\Config;
use think\Session;
use think\Cache;

class Aly extends Controller
{
   // private $_apiUrl = 'http://alirm-com.konpn.com';

   private $_apiUrl = 'https://finance.market.alicloudapi.com/neipan'; 
   private $_appcode = '********************************';
    
   private $_tradeType = '';
   private $_apiType = '';
    
   public function _initialize() {
       $type = Config::get('site.api_type');
       $this->_tradeType = Config::get('site.trade_type');
       $this->_apiType = $type;
       $this->_appcode = Config::get('site.appcode') ?: $this->_appcode;
   }
    
   public function start() {
       $code = 'BTC';
       $type = '1M';
       $start = date('Y-m-d H:i:s', time() - 60*500 + 1);
       $end = date('Y-m-d H:i:s', time());
        
       $url = $this->_apiUrl . "/query/comlstkm?fromtick=".time()."&period=1M,5M&symbol=".$code;
       $result = stock_request($url);
       fh($result['Obj'][0]);
   }
    
   /**
    * 获取最新K线数据
    */
   public function runLastData() {
       $type = $this->request->param('type');

       $types = array('1min', '5min', '30min', '1hour', '1day');

       if (!in_array($type, $types)) {
           return;
       }

       // 添加进程锁，防止重复执行
       $lockFile = RUNTIME_PATH . 'lock_' . $type . '.txt';
       if (file_exists($lockFile) && (time() - filemtime($lockFile)) < 30) {
           exit('Process already running');
       }
       file_put_contents($lockFile, time());

       $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>2))->select();

       // 直接在当前进程中循环处理，不创建子进程
       foreach ($stocks as $stock) {
           $code = $stock['code'];
           // 直接调用方法，而不是创建子进程
           $this->getLastData($code, $type);

           // 每个产品处理完后稍微休息，避免API调用过于频繁
           usleep(100000); // 0.1秒
       }

       // 清理锁文件
       if (file_exists($lockFile)) {
           unlink($lockFile);
       }

       // 释放内存
       unset($stocks);
       gc_collect_cycles();
       exit(0);
   }
   
    
      /**
     * 获取最新价数据脚本
     */
    public function runStock() {
        $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>2))->select();

        // 直接在当前进程中循环处理，不创建子进程
        foreach ($stocks as $stock) {
            $code = $stock['code'];
            // 直接调用方法，而不是创建子进程
            $this->getStock($code);

            // 每个产品处理完后稍微休息，避免API调用过于频繁
            usleep(100000); // 0.1秒
        }

        // 释放内存
        unset($stocks);
        gc_collect_cycles();
        exit(0);
    }
      /**
     * 获取最新价数据脚本
     */
    public function runStock1() {
        $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>3))->select();

        // 直接在当前进程中循环处理，不创建子进程
        foreach ($stocks as $stock) {
            $code = $stock['code'];
            // 直接调用方法，而不是创建子进程
            $this->getStock($code);

            // 每个产品处理完后稍微休息，避免API调用过于频繁
            usleep(100000); // 0.1秒
        }

        // 释放内存
        unset($stocks);
        gc_collect_cycles();
        exit(0);
    }
    
    /**
     * 获取商品价格信息
     */
public function getStock($code = null) {
    // 优先使用传入的参数，如果没有则从请求中获取
    $code = $code ?: $this->request->param('code');

    // 使用阿里云推荐的参数名称
    $url = $this->_apiUrl . "/real?symbol=".$code;

    $result = $this->aliyunRequest($url);

    if (!isset($result['data']) || !$result['data']) {
        return;
    }

    // 根据实际返回结构调整数据解析
    $snapshot = $result['data']['snapshot'] ?? $result['data'];
    $res = $this->getFormat($snapshot,$code);

    if ($res) {
        Cache::set($code.'_stock', serialize($res));
    }

    // 释放内存
    unset($result, $snapshot, $res);
}
    
    
    /**
     * 获取最新K线数据
     * @return boolean
     */
     public function getLastData($code = null, $type = null) {
    // 优先使用传入的参数，如果没有则从请求中获取
    $code = $code ?: $this->request->param('code');
    $type = $type ?: $this->request->param('type');

    if (!in_array($type, array('1min', '5min', '30min', '1hour', '1day'))) {
        return false;
    }

    // 检查缓存，避免重复API调用
    $cacheKey = $code . '_kline_' . $type;
    $cacheTime = array(
        '1min' => 60,    // 1分钟缓存1分钟
        '5min' => 300,   // 5分钟缓存5分钟
        '30min' => 1800, // 30分钟缓存30分钟
        '1hour' => 3600, // 1小时缓存1小时
        '1day' => 7200   // 1天缓存2小时
    );

    $cached = Cache::get($cacheKey);
    if ($cached && (time() - $cached['timestamp']) < $cacheTime[$type]) {
        return true; // 使用缓存数据，避免API调用
    }

    // 映射K线类型到阿里云参数
    $typeMap = array(
        '1min' => 1,
        '5min' => 5,
        '30min' => 30,
        '1hour' => 60,
        '1day' => 1440
    );

    // 减少K线数据量，降低内存占用
    $limit = 100; // 进一步减少到100条K线数据
    $klineType = $typeMap[$type];

    // 构建请求URL
    $url = $this->_apiUrl . "/kline?symbol=".$code."&type=".$klineType."&limit=".$limit;

    $result = $this->aliyunRequest($url);

    if (!isset($result['code']) || $result['code'] != 1) {
        return false;
    }

    $data = $this->getFormatLine($result, $code);
    if ($data) {
        // 存储最新一条K线
        $lastData = array_slice($data, -1, 1);
        if ($lastData[0]) {
            Cache::set($code.'_stock_new_'.$type, serialize($lastData[0]));
        }

        // 存储全部K线数据
        Cache::set($code.'_stock_'.$type, serialize($data));
    }

    // 释放内存
    unset($result, $data, $lastData);
    return true;
}
    
    
    /**
     * 获取格式化K线数据
     * @param unknown $data
     * @return boolean|unknown[][]|mixed[][]
     */
     
     private function aliyunRequest($url) {
    $headers = [
        "Authorization: APPCODE " . $this->_appcode,
        "Content-Type: application/json; charset=utf-8"
    ];

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL            => $url,
        CURLOPT_HTTPHEADER     => $headers,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_TIMEOUT        => 10
    ]);

    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        curl_close($ch);
        return ['code' => 0, 'msg' => 'CURL请求失败'];
    }
    curl_close($ch);

    $result = json_decode($response, true) ?: [];

    // 释放内存
    unset($response);

    return $result;
}
  private function getFormatLine($data, $code) {
    $returnData = [];

    // 检查 'data' 和 'data.lines' 是否存在且为数组
    if (!isset($data['data']['lines']) || !is_array($data['data']['lines'])) {
        return [];
    }

    // 阿里云返回的K线数据是索引数组，格式: [开, 收, 高, 低, 涨跌, 涨跌幅, 量, 时间戳]
    foreach ($data['data']['lines'] as $item) {
        try {
            if (count($item) < 8) continue; // 数据项不完整则跳过

            $timestamp = $item[7]; // 秒级时间戳
            $time = $timestamp * 1000; // 转换为毫秒时间戳

            $returnData[] = [
                'time'     => $time,
                'open'     => floatval($item[0]),
                'close'    => floatval($item[1]),
                'high'     => floatval($item[2]),
                'low'      => floatval($item[3]),
                'volume'   => floatval($item[6]),
                'datetime' => date('Y-m-d H:i:s', $timestamp)
            ];
        } catch (\Exception $e) {
            continue;
        }
    }

    // 按时间升序排序
    usort($returnData, function($a, $b) {
        return $a['time'] <=> $b['time'];
    });

    return $returnData;
}
    
   
    /**
     * 格式化最新数据
     * @param unknown $data
     * @param unknown $code
     * @return boolean|unknown[]|string[]|NULL[]|mixed[]|boolean[]
     */
   private function getFormat($data,$code) {
    if (empty($data)) {
        return false;
    }

    // 阿里云返回的数据结构示例：
    // {
    //   "msg":"ok",
    //   "code":1,
    //   "data":{
    //     "ask":"69960.000",
    //     "bid":"69940.000",
    //     "price":"69960.000",
    //     "high":"70980.000",
    //     "low":"68080.000",
    //     "open":"68980.000",
    //     "volume":"1206323",
    //     "change":"1,920.000",
    //     "changeRate":"2.82",
    //     "update_time":1752822275,
    //     "name":"碳酸锂连续"
    //   }
    // }
    
    $result = [
        'code' => $code,  // 直接使用传入的 $code 参数
        'price_high'   => isset($data['high']) ? floatval(str_replace(',', '', $data['high'])) : 0,
        'price_low'    => isset($data['low']) ? floatval(str_replace(',', '', $data['low'])) : 0,
        'price'        => isset($data['price']) ? number_format(floatval(str_replace(',', '', $data['price'])), 3, '.', '') : '0.000',
        'vol'          => isset($data['volume']) ? floatval(str_replace(',', '', $data['volume'])) : 0,
        'open_price'   => isset($data['open']) ? floatval(str_replace(',', '', $data['open'])) : 0,
        'price_zf'     => isset($data['changeRate']) ? floatval(str_replace('%', '', $data['changeRate'])) : 0,
        'price_update' => date('Y-m-d H:i:s'),
        'time'         => date('Y-m-d H:i:s', $data['update_time'] ?? time())
    ];

    // 更新数据库
    if (!empty($result['code'])) {
        Db::name('product')->where('code', $result['code'])->update($result);
    }

    return $result;
}
    
    /**
     * 获取24H前信息
     * @param unknown $code
     * @return mixed|boolean
     */
    private function get24HData($code) {
        
        $key = $code.'_stock_open';
        $openPrice = 0;
        $history = unserialize(Cache::get($key));
        $time = strtotime(" -24 hours", strtotime(date('Y-m-d H:i').':00'))*1000;
        if (isset($history[$time])) {
            $openPrice = $history[$time]['open'];
        }
        
        if ($openPrice > 0) {
            return $openPrice;
        }
        return false;
    }


}