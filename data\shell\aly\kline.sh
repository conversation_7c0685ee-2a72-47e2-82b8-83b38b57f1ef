#!/bin/bash
cd ../../

# 检查是否已有进程在运行
PIDFILE="/tmp/kline_1min.pid"

# 如果PID文件存在且进程还在运行，则退出
if [ -f "$PIDFILE" ]; then
    PID=$(cat "$PIDFILE")
    if ps -p $PID > /dev/null 2>&1; then
        echo "Process already running with PID $PID"
        exit 1
    fi
fi

# 写入当前进程PID
echo $$ > "$PIDFILE"

# 清理函数
cleanup() {
    rm -f "$PIDFILE"
    exit 0
}

# 设置信号处理
trap cleanup SIGTERM SIGINT

while true
do
    # 检查是否在交易时间内（可选优化）
    HOUR=$(date +%H)
    if [ $HOUR -ge 9 ] && [ $HOUR -le 16 ]; then
        /www/server/php/70/bin/php stock.php Aly/runLastData/type/1min > /dev/null 2>&1
    fi

    # 增加休眠时间，从20秒改为60秒
    sleep 60s;
done