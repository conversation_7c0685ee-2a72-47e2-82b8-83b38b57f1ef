#!/bin/bash
# 监控脚本 - 检查当前运行的股票采集进程

echo "=== Stock Data Collection Process Monitor ==="
echo "Time: $(date)"
echo ""

# 检查PHP进程
echo "=== Running PHP Processes ==="
ps aux | grep -E "(stock\.php|Aly)" | grep -v grep | while read line; do
    echo "$line"
done
echo ""

# 检查PID文件
echo "=== PID Files Status ==="
for pidfile in /tmp/optimized_stock.pid /tmp/kline_1min.pid; do
    if [ -f "$pidfile" ]; then
        PID=$(cat "$pidfile")
        if ps -p $PID > /dev/null 2>&1; then
            echo "$pidfile: RUNNING (PID: $PID)"
        else
            echo "$pidfile: STALE (PID: $PID not running)"
        fi
    else
        echo "$pidfile: NOT FOUND"
    fi
done
echo ""

# 检查Redis连接数
echo "=== Redis Status ==="
if command -v redis-cli &> /dev/null; then
    echo "Redis connections: $(redis-cli info clients | grep connected_clients)"
    echo "Redis memory usage: $(redis-cli info memory | grep used_memory_human)"
    echo "Redis operations per second: $(redis-cli info stats | grep instantaneous_ops_per_sec)"
else
    echo "Redis CLI not available"
fi
echo ""

# 检查系统负载
echo "=== System Load ==="
echo "Load average: $(uptime | awk -F'load average:' '{print $2}')"
echo "Memory usage: $(free -h | grep Mem)"
echo ""

# 检查日志文件大小
echo "=== Log Files ==="
for logfile in /www/wwwroot/127.0.0.3/runtime/log/*.log; do
    if [ -f "$logfile" ]; then
        size=$(du -h "$logfile" | cut -f1)
        echo "$(basename $logfile): $size"
    fi
done
echo ""

# 检查最近的错误日志
echo "=== Recent Errors (last 10 lines) ==="
if [ -f "/www/wwwroot/127.0.0.3/runtime/log/error.log" ]; then
    tail -10 /www/wwwroot/127.0.0.3/runtime/log/error.log
else
    echo "No error log found"
fi

echo ""
echo "=== Monitor Complete ==="
