#!/bin/bash
# 获取最新价格数据脚本 - 改进版本

# 设置工作目录
cd /www/wwwroot/*********/

# 创建日志文件
LOG_FILE="/tmp/stock_$(date +%Y%m%d).log"
PID_FILE="/tmp/stock.pid"

# 记录脚本开始
echo "$(date '+%Y-%m-%d %H:%M:%S') - Stock script started" >> $LOG_FILE

# 保存进程ID
echo $$ > $PID_FILE

# 检查PHP路径
PHP_PATH=""
if [ -f "/www/server/php/70/bin/php" ]; then
    PHP_PATH="/www/server/php/70/bin/php"
elif [ -f "/usr/bin/php" ]; then
    PHP_PATH="/usr/bin/php"
else
    PHP_PATH="php"
fi

echo "$(date '+%Y-%m-%d %H:%M:%S') - Using PHP: $PHP_PATH" >> $LOG_FILE

# 执行600次，每次间隔1秒
int=1;
while(( int <= 600 ));
do
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Execution #$int" >> $LOG_FILE

    # 执行PHP脚本并记录输出
    $PHP_PATH stock.php Aly/runStock >> $LOG_FILE 2>&1
    exit_code=$?

    if [ $exit_code -ne 0 ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Error: Exit code $exit_code" >> $LOG_FILE
    fi

    let "int++";
    sleep 1;
done

# 清理PID文件
rm -f $PID_FILE

echo "$(date '+%Y-%m-%d %H:%M:%S') - Stock script completed" >> $LOG_FILE