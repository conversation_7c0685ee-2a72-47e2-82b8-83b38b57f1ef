<?php
// 设置错误报告级别
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 设置错误处理器
set_error_handler(function($severity, $message, $file, $line) {
    echo "错误: $message\n";
    echo "文件: $file\n";
    echo "行号: $line\n";
    echo "严重性: $severity\n";
    echo "---\n";
    
    // 如果是花括号语法错误，显示更多信息
    if (strpos($message, 'curly braces') !== false) {
        echo "这是PHP 8不支持的花括号数组访问语法错误\n";
        echo "需要将 \$array{\$index} 改为 \$array[\$index]\n";
    }
    
    return false; // 让PHP继续处理错误
});

define('APP_PATH', __DIR__ . '/application/');
require __DIR__ . '/thinkphp/start.php';

use think\Db;

echo "=== 测试数据库查询 ===\n";

try {
    $stocks = Db::name('product')->where(['status' => 1, 'is_open' => 1, 'cid' => 2])->select();
    echo "查询成功，找到 " . count($stocks) . " 个产品\n";
} catch (Exception $e) {
    echo "异常: " . $e->getMessage() . "\n";
}

echo "=== 测试完成 ===\n";
