<?php
namespace app\stock\controller;

use think\Controller;
use think\Request;
use think\Db;
use think\Config;
use think\Session;
use think\Cache;

class Aly extends Controller
{
   // private $_apiUrl = 'http://alirm-com.konpn.com';

   private $_apiUrl = 'https://finance.market.alicloudapi.com/neipan'; 
   private $_appcode = '********************************';
    
   private $_tradeType = '';
   private $_apiType = '';
    
   public function _initialize() {
       $type = Config::get('site.api_type');
       $this->_tradeType = Config::get('site.trade_type');
       $this->_apiType = $type;
       $this->_appcode = Config::get('site.appcode') ?: $this->_appcode;
   }
    
   public function start() {
       $code = 'BTC';
       $type = '1M';
       $start = date('Y-m-d H:i:s', time() - 60*500 + 1);
       $end = date('Y-m-d H:i:s', time());
        
       $url = $this->_apiUrl . "/query/comlstkm?fromtick=".time()."&period=1M,5M&symbol=".$code;
       $result = stock_request($url);
       fh($result['Obj'][0]);
   }
    
   /**
    * 获取最新K线数据
    */
   public function runLastData() {
       $type = $this->request->param('type');
        
       $types = array('1min', '5min', '30min', '1hour', '1day');
        
       if (!in_array($type, $types)) {
           return;
       }
        
       $time = time();
       $cmds = array();
        
       $path = dirname(dirname(dirname(dirname(__FILE__)))).'/stock.php ';
        
       $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>2))->select();
        
       foreach ($stocks as $key=>$stock) {
           $code = $stock['code'];
           $cmds[] = 'php '.$path.'  Aly/getLastData/type/'.$type.'/code/'.$code;
       }
     
       pcntl_signal(SIGCHLD, SIG_IGN);	//如果父进程不关心子进程什么时候结束,子进程结束后，内核会回收。
       foreach ($cmds as  $cmd) {
           $pid = pcntl_fork();	//创建子进程
           //父进程和子进程都会执行下面代码
           if ($pid == -1) {
               //错误处理：创建子进程失败时返回-1.
               die('could not fork');
           } else if ($pid) {
               //父进程会得到子进程号，所以这里是父进程执行的逻辑
               //如果不需要阻塞进程，而又想得到子进程的退出状态，则可以注释掉pcntl_wait($status)语句，或写成：
               $status = 0;
               pcntl_wait($status, WNOHANG); //等待子进程中断，防止子进程成为僵尸进程。
           } else {
               //子进程得到的$pid为0, 所以这里是子进程执行的逻辑。
               echo shell_exec($cmd);
               exit(0);
           }
       }
       exit(0);
   }
   
    
      /**
     * 获取最新价数据脚本
     */
    public function runStock() {
        $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>2  ))->select();
        $time = time();
        $cmds = array();
        $path = dirname(dirname(dirname(dirname(__FILE__)))).'/stock.php ';
        
        $runObj = array();
        foreach ($stocks as $key=>$val) {
            $runObj[] = $val['code'];
        }
        
        foreach ($runObj as $key=>$val) {
            $cmds[] = 'php '.$path.'  Aly/getStock/code/'.$val;
            
            //  $cmds[] = 'php '.$path.'  Aly/getStock/code/'.implode(',', $val);
            
            //var_dump($cmds);
        }
        
        pcntl_signal(SIGCHLD, SIG_IGN);	//如果父进程不关心子进程什么时候结束,子进程结束后，内核会回收。
        foreach ($cmds as  $cmd) {
            $pid = pcntl_fork();	//创建子进程
            //父进程和子进程都会执行下面代码
            if ($pid == -1) {
                //错误处理：创建子进程失败时返回-1.
                die('could not fork');
            } else if ($pid) {
                //父进程会得到子进程号，所以这里是父进程执行的逻辑
                //如果不需要阻塞进程，而又想得到子进程的退出状态，则可以注释掉pcntl_wait($status)语句，或写成：
                $status = 0;
                pcntl_wait($status, WNOHANG); //等待子进程中断，防止子进程成为僵尸进程。
            } else {
                //子进程得到的$pid为0, 所以这里是子进程执行的逻辑。
                echo shell_exec($cmd);
                exit(0) ;
            }
        }
        exit(0);
    }
      /**
     * 获取最新价数据脚本
     */
    public function runStock1() {
        $stocks = Db::name('product')->where(array('status'=>1, 'is_open'=>1, 'cid'=>3))->select();
        $time = time();
        $cmds = array();
        $path = dirname(dirname(dirname(dirname(__FILE__)))).'/stock.php ';
        
        $runObj = array();
        foreach ($stocks as $key=>$val) {
            $runObj[] = $val['code'];
        }
        
        foreach ($runObj as $key=>$val) {
            $cmds[] = 'php '.$path.'  Aly/getStock/code/'.$val;
            
            //  $cmds[] = 'php '.$path.'  Aly/getStock/code/'.implode(',', $val);
            
            //var_dump($cmds);
        }
        
        pcntl_signal(SIGCHLD, SIG_IGN);	//如果父进程不关心子进程什么时候结束,子进程结束后，内核会回收。
        foreach ($cmds as  $cmd) {
            $pid = pcntl_fork();	//创建子进程
            //父进程和子进程都会执行下面代码
            if ($pid == -1) {
                //错误处理：创建子进程失败时返回-1.
                die('could not fork');
            } else if ($pid) {
                //父进程会得到子进程号，所以这里是父进程执行的逻辑
                //如果不需要阻塞进程，而又想得到子进程的退出状态，则可以注释掉pcntl_wait($status)语句，或写成：
                $status = 0;
                pcntl_wait($status, WNOHANG); //等待子进程中断，防止子进程成为僵尸进程。
            } else {
                //子进程得到的$pid为0, 所以这里是子进程执行的逻辑。
                echo shell_exec($cmd);
                exit(0) ;
            }
        }
        exit(0);
    }  
    
    /**
     * 获取商品价格信息
     */
public function getStock($code = null) {
    // 优先使用传入的参数，如果没有则从请求中获取
    $code = $code ?: $this->request->param('code');
    
    // 使用阿里云推荐的参数名称
    $url = $this->_apiUrl . "/real?symbol=".$code;
    
    $result = $this->aliyunRequest($url);
    
    if (!isset($result['data']) || !$result['data']) {
        $this->writeLog('aly_error.log', 
            '实时行情数据获取失败:'.$code.PHP_EOL);
        return;
    }
    
    // 根据实际返回结构调整数据解析
    $snapshot = $result['data']['snapshot'] ?? $result['data'];
    $res = $this->getFormat($snapshot,$code);
    
    if ($res) {
        Cache::set($code.'_stock', serialize($res));
        echo $code.PHP_EOL;
    }
}
    
    
    /**
     * 获取最新K线数据
     * @return boolean
     */
     public function getLastData($code = null, $type = null) {
    // 优先使用传入的参数，如果没有则从请求中获取
    $code = $code ?: $this->request->param('code');
    $type = $type ?: $this->request->param('type');
    
    if (!in_array($type, array('1min', '5min', '30min', '1hour', '1day'))) {
        return false;
    }
    
    // 映射K线类型到阿里云参数
    $typeMap = array(
        '1min' => 1,
        '5min' => 5,
        '30min' => 30,
        '1hour' => 60,
        '1day' => 1440
    );
    
    $limit = 500; // 获取500条K线数据
    $klineType = $typeMap[$type];
    
    // 构建请求URL
    $url = $this->_apiUrl . "/kline?symbol=".$code."&type=".$klineType."&limit=".$limit;
    
    $result = $this->aliyunRequest($url);
    
    // 调试日志
    $this->writeLog('aly_debug.log', 
        date('Y-m-d H:i:s')." K线请求:".$url."\n响应:".json_encode($result)."\n\n");
    
    if (!isset($result['code']) || $result['code'] != 1) { // 阿里云K线接口成功code为1
        $this->writeLog('aly_error.log', 
            'K线数据错误:'.json_encode($result)." 代码:".$code.PHP_EOL);
        return false;
    }
    
    $data = $this->getFormatLine($result, $code);
    if ($data) {
        // 存储最新一条K线
        $lastData = array_slice($data, -1, 1);
        if ($lastData[0]) {
            Cache::set($code.'_stock_new_'.$type, serialize($lastData[0]));
            echo $code.'_stock_new_'.$type.PHP_EOL;
        }
        
        // 存储全部K线数据
        Cache::set($code.'_stock_'.$type, serialize($data));
        echo $code.'_stock_'.$type.PHP_EOL;
    }
    return true;
}
    
    
    /**
     * 获取格式化K线数据
     * @param unknown $data
     * @return boolean|unknown[][]|mixed[][]
     */
     
     private function aliyunRequest($url) {
    $headers = [
        "Authorization: APPCODE " . $this->_appcode,
        "Content-Type: application/json; charset=utf-8"
    ];

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL            => $url,
        CURLOPT_HTTPHEADER     => $headers,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false,
        CURLOPT_TIMEOUT        => 10
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        $this->writeLog('aly_error.log', 
            date('Y-m-d H:i:s')." CURL错误: ".curl_error($ch)." [URL: {$url}]".PHP_EOL);
        curl_close($ch);
        return ['code' => 0, 'msg' => 'CURL请求失败'];
    }
    curl_close($ch);

    // 记录请求日志（调试用）
    $this->writeLog('aly_request.log', 
        date('Y-m-d H:i:s')." Request: {$url}\nResponse: {$response}\n\n");

    return json_decode($response, true) ?: [];
}
  private function getFormatLine($data, $code) {
    $returnData = [];
    
    // 检查 'data' 和 'data.lines' 是否存在且为数组
    if (!isset($data['data']['lines']) || !is_array($data['data']['lines'])) {
        $this->writeLog('aly_error.log', "K线数据格式错误 for code {$code}: " . json_encode($data));
        return [];
    }

    // 阿里云返回的K线数据是索引数组，格式: [开, 收, 高, 低, 涨跌, 涨跌幅, 量, 时间戳]
    foreach ($data['data']['lines'] as $item) {
        try {
            if (count($item) < 8) continue; // 数据项不完整则跳过

            $timestamp = $item[7]; // 秒级时间戳
            $time = $timestamp * 1000; // 转换为毫秒时间戳

            $returnData[] = [
                'time'     => $time,
                'open'     => floatval($item[0]),
                'close'    => floatval($item[1]),
                'high'     => floatval($item[2]),
                'low'      => floatval($item[3]),
                'volume'   => floatval($item[6]),
                'datetime' => date('Y-m-d H:i:s', $timestamp)
            ];
        } catch (\Exception $e) {
            $this->writeLog('aly_error.log', "K线数据解析异常 for code {$code}: " . $e->getMessage());
            continue;
        }
    }

    // 按时间升序排序
    usort($returnData, function($a, $b) {
        return $a['time'] <=> $b['time'];
    });
    
    return $returnData;
}
    
   
    /**
     * 格式化最新数据
     * @param unknown $data
     * @param unknown $code
     * @return boolean|unknown[]|string[]|NULL[]|mixed[]|boolean[]
     */
   private function getFormat($data,$code) {
    if (empty($data)) {
        return false;
    }

    // 阿里云返回的数据结构示例：
    // {
    //   "msg":"ok",
    //   "code":1,
    //   "data":{
    //     "ask":"69960.000",
    //     "bid":"69940.000",
    //     "price":"69960.000",
    //     "high":"70980.000",
    //     "low":"68080.000",
    //     "open":"68980.000",
    //     "volume":"1206323",
    //     "change":"1,920.000",
    //     "changeRate":"2.82",
    //     "update_time":1752822275,
    //     "name":"碳酸锂连续"
    //   }
    // }
    
    $result = [
        'code' => $code,  // 直接使用传入的 $code 参数
        'price_high'   => isset($data['high']) ? floatval(str_replace(',', '', $data['high'])) : 0,
        'price_low'    => isset($data['low']) ? floatval(str_replace(',', '', $data['low'])) : 0,
        'price'        => isset($data['price']) ? number_format(floatval(str_replace(',', '', $data['price'])), 3, '.', '') : '0.000',
        'vol'          => isset($data['volume']) ? floatval(str_replace(',', '', $data['volume'])) : 0,
        'open_price'   => isset($data['open']) ? floatval(str_replace(',', '', $data['open'])) : 0,
        'price_zf'     => isset($data['changeRate']) ? floatval(str_replace('%', '', $data['changeRate'])) : 0,
        'price_update' => date('Y-m-d H:i:s'),
        'time'         => date('Y-m-d H:i:s', $data['update_time'] ?? time())
    ];

    // 更新数据库
    if (!empty($result['code'])) {
        Db::name('product')->where('code', $result['code'])->update($result);
    }

    return $result;
}
    
    /**
     * 获取24H前信息
     * @param unknown $code
     * @return mixed|boolean
     */
    private function get24HData($code) {
        
        $key = $code.'_stock_open';
        $openPrice = 0;
        $history = unserialize(Cache::get($key));
        $time = strtotime(" -24 hours", strtotime(date('Y-m-d H:i').':00'))*1000;
        if (isset($history[$time])) {
            $openPrice = $history[$time]['open'];
        }
        
        if ($openPrice > 0) {
            return $openPrice;
        }
        return false;
    }

    /**
     * 记录日志并进行日志轮转
     * @param string $file 日志文件名
     * @param string $message 日志消息
     */
    private function writeLog($file, $message) {
        $logFile = ROOT_PATH . 'runtime/' . $file;
        
        // 如果日志文件超过10MB，进行轮转
        if (file_exists($logFile) && filesize($logFile) > 10 * 1024 * 1024) {
            rename($logFile, $logFile . '.' . date('YmdHis'));
        }
        
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - " . $message . "\n", FILE_APPEND);
    }
}