<?php
// 测试价格更新流程
echo "=== 测试价格更新流程 ===\n";

define('APP_PATH', __DIR__ . '/application/');
define('BIND_MODULE','stock');
require __DIR__ . '/thinkphp/start.php';

try {
    echo "1. 查看更新前的数据...\n";
    $beforeData = \think\Db::name('product')->where('code', 'AG0')->find();
    if ($beforeData) {
        echo "更新前价格: " . $beforeData['price'] . "\n";
        echo "更新前最高价: " . $beforeData['price_high'] . "\n";
        echo "更新前最低价: " . $beforeData['price_low'] . "\n";
        echo "更新前开盘价: " . $beforeData['open_price'] . "\n";
        echo "更新前时间: " . $beforeData['price_update'] . "\n";
    }
    
    echo "\n2. 执行价格更新...\n";
    $aly = new \app\stock\controller\Aly();
    $aly->getStock('AG0');
    
    echo "\n3. 查看更新后的数据...\n";
    $afterData = \think\Db::name('product')->where('code', 'AG0')->find();
    if ($afterData) {
        echo "更新后价格: " . $afterData['price'] . "\n";
        echo "更新后最高价: " . $afterData['price_high'] . "\n";
        echo "更新后最低价: " . $afterData['price_low'] . "\n";
        echo "更新后开盘价: " . $afterData['open_price'] . "\n";
        echo "更新后时间: " . $afterData['price_update'] . "\n";
    }
    
    echo "\n4. 比较变化...\n";
    if ($beforeData && $afterData) {
        if ($beforeData['price'] != $afterData['price']) {
            echo "✅ 价格已更新: " . $beforeData['price'] . " -> " . $afterData['price'] . "\n";
        } else {
            echo "❌ 价格未更新，仍为: " . $afterData['price'] . "\n";
        }
        
        if ($beforeData['price_update'] != $afterData['price_update']) {
            echo "✅ 更新时间已更新: " . $beforeData['price_update'] . " -> " . $afterData['price_update'] . "\n";
        } else {
            echo "❌ 更新时间未更新\n";
        }
    }
    
    echo "\n5. 测试API接口返回...\n";
    $stocks = getStock(array('status'=>1, 'is_open'=>1, 'code'=>'AG0'));
    if (!empty($stocks[0])) {
        $stock = $stocks[0];
        echo "API返回价格: " . $stock['price'] . "\n";
        echo "API返回最高价: " . $stock['price_high'] . "\n";
        echo "API返回最低价: " . $stock['price_low'] . "\n";
        echo "API返回开盘价: " . $stock['open_price'] . "\n";
    }
    
    echo "\n=== 测试完成 ===\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
