#!/bin/bash
# 检查股票数据采集状态

echo "=== 股票数据采集状态检查 ==="
echo "检查时间: $(date)"
echo ""

# 检查进程是否运行
echo "1. 检查运行中的进程:"
ps aux | grep -E "(stock\.php|Aly)" | grep -v grep
echo ""

# 检查PID文件
echo "2. 检查PID文件:"
if [ -f "/tmp/stock.pid" ]; then
    PID=$(cat /tmp/stock.pid)
    echo "Stock PID文件存在: $PID"
    if ps -p $PID > /dev/null 2>&1; then
        echo "进程 $PID 正在运行"
    else
        echo "进程 $PID 已停止"
    fi
else
    echo "Stock PID文件不存在"
fi
echo ""

# 检查日志文件
echo "3. 检查今日日志:"
TODAY_LOG="/tmp/stock_$(date +%Y%m%d).log"
if [ -f "$TODAY_LOG" ]; then
    echo "日志文件: $TODAY_LOG"
    echo "文件大小: $(ls -lh $TODAY_LOG | awk '{print $5}')"
    echo "最后10行:"
    tail -10 "$TODAY_LOG"
else
    echo "今日日志文件不存在"
fi
echo ""

# 检查数据库最新更新时间
echo "4. 检查数据库更新状态:"
cd /www/wwwroot/127.0.0.3/
php -r "
require 'vendor/autoload.php';
use think\Db;
use think\Config;

// 加载配置
Config::load('application/database.php');

try {
    \$latest = Db::name('product')
        ->where('status', 1)
        ->where('is_open', 1)
        ->where('cid', 2)
        ->order('price_update desc')
        ->limit(5)
        ->field('code,price,price_update')
        ->select();
    
    if (\$latest) {
        echo \"最新更新的5个产品:\n\";
        foreach (\$latest as \$item) {
            echo \"代码: {\$item['code']}, 价格: {\$item['price']}, 更新时间: {\$item['price_update']}\n\";
        }
    } else {
        echo \"没有找到产品数据\n\";
    }
} catch (Exception \$e) {
    echo \"数据库查询错误: \" . \$e->getMessage() . \"\n\";
}
"
echo ""

echo "=== 检查完成 ==="
